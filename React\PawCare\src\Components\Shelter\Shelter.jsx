import React from 'react'
import shelpic from '../../assets/imgs/shelpic.jpg'
import VetCard from '../ClinicCard/ClinicCard'
import { FaPaw, FaHeart, FaHandHoldingHeart, FaHome } from 'react-icons/fa'

const Shelter = () => {
  return (
    <>

      <div >
        <section>
          <div className='p-10'>
            <div className='grid grid-cols-2 w-full rounded-4xl p-10 relative bg-white shadow-lg border border-gray-100'>
              <div className='flex flex-col justify-center pr-8'>
                <div className="inline-block bg-blue-100 text-blue-600 px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Find Your Forever Friend
                </div>
                <h1 className='text-6xl font-bold text-gray-800 leading-tight'>
                  culpa qui officia deserunt
                </h1>

                <div className='pt-6'>
                  <span className='block text-xl font-normal text-gray-600'>
                    <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur Excepteur sint.</p>
                  </span>
                </div>

                <div className='flex gap-5 mt-8'>
                  <button className='bg-blue-500 text-white px-6 py-3 rounded-full hover:scale-105 transition duration-300 transform hover:shadow-lg shadow-blue-200 font-medium flex items-center'>
                    <FaHandHoldingHeart className="mr-2" /> Ask Anything
                  </button>
                  <button className='bg-white text-blue-500 border-2 border-blue-500 px-6 py-3 rounded-full hover:scale-105 transition duration-300 transform hover:shadow-lg shadow-blue-100 font-medium flex items-center'>
                    <FaHeart className="mr-2" /> Donate Now
                  </button>
                </div>
              </div>

              <div className='flex justify-center items-center'>
                <div className="relative">
                  <div className="absolute -inset-4 bg-blue-100 rounded-full opacity-50 blur-xl"></div>
                  <img
                    className='h-100 object-contain relative rounded-3xl shadow-lg hover:scale-105 transition-transform duration-500'
                    src={shelpic}
                    alt="Dog in shelter"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>


      <section className="py-16 bg-white">
        <div className="container mx-auto px-10 text-center max-w-4xl">
          <h2 className="text-3xl font-bold mb-8 text-gray-800">Our Mission</h2>
          <p className="text-xl text-gray-600 mb-10 leading-relaxed">
            We are dedicated to finding loving homes for animals in need. Our shelters provide care,
            rehabilitation, and a second chance for pets waiting for their forever families.
          </p>

          <div className="grid grid-cols-4 gap-8 mt-12">
            <div className="flex flex-col items-center">
              <div className="bg-blue-100 p-4 rounded-full text-blue-600 mb-4">
                <FaPaw size={24} />
              </div>
              <h3 className="font-bold text-lg mb-2">Rescue</h3>
              <p className="text-gray-600 text-sm">Saving animals from harmful situations</p>
            </div>

            <div className="flex flex-col items-center">
              <div className="bg-blue-100 p-4 rounded-full text-blue-600 mb-4">
                <FaHeart size={24} />
              </div>
              <h3 className="font-bold text-lg mb-2">Care</h3>
              <p className="text-gray-600 text-sm">Providing medical attention and love</p>
            </div>

            <div className="flex flex-col items-center">
              <div className="bg-blue-100 p-4 rounded-full text-blue-600 mb-4">
                <FaHandHoldingHeart size={24} />
              </div>
              <h3 className="font-bold text-lg mb-2">Support</h3>
              <p className="text-gray-600 text-sm">Helping animals adjust and thrive</p>
            </div>

            <div className="flex flex-col items-center">
              <div className="bg-blue-100 p-4 rounded-full text-blue-600 mb-4">
                <FaHome size={24} />
              </div>
              <h3 className="font-bold text-lg mb-2">Adopt</h3>
              <p className="text-gray-600 text-sm">Finding forever homes for our animals</p>
            </div>
          </div>
        </div>
      </section>


      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-800">Our Shelter Locations</h2>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Visit one of our shelter locations to meet our amazing animals and dedicated staff.
              We're here to help you find your perfect companion.
            </p>
          </div>

          <div className='w-full flex justify-center gap-6 flex-wrap'>
            <VetCard img='https://tse2.mm.bing.net/th?id=OIP.8TrfOkjEpDbgquhs-IhbKQHaEZ&pid=Api&P=0&h=220' />
            <VetCard img='https://tse2.mm.bing.net/th?id=OIP.8TrfOkjEpDbgquhs-IhbKQHaEZ&pid=Api&P=0&h=220' />
            <VetCard img='https://tse2.mm.bing.net/th?id=OIP.8TrfOkjEpDbgquhs-IhbKQHaEZ&pid=Api&P=0&h=220' />
            <VetCard img='https://tse2.mm.bing.net/th?id=OIP.8TrfOkjEpDbgquhs-IhbKQHaEZ&pid=Api&P=0&h=220' />
            <VetCard img='https://tse2.mm.bing.net/th?id=OIP.8TrfOkjEpDbgquhs-IhbKQHaEZ&pid=Api&P=0&h=220' />
          </div>
        </div>
      </section>

      
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-10 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Make a Difference?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Whether you're looking to adopt, volunteer, or donate, your support helps us continue our mission of finding homes for animals in need.
          </p>
          <div className="flex justify-center gap-4">
            <button className="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition duration-300 shadow-md">
              Adopt Now
            </button>
            <button className="bg-transparent text-white border-2 border-white px-8 py-3 rounded-full font-semibold hover:bg-blue-700 transition duration-300">
              Volunteer
            </button>
          </div>
        </div>
      </section>
    </>
  )
}

export default Shelter
