// Stripe Configuration
// Replace with your actual Stripe publishable key
export const STRIPE_PUBLISHABLE_KEY = "pk_test_51RSxcXQkL9Zh5RwU8N6bzjsqGmgobmJGE0R55TceiDypE7EtTXICzSeYtld9pe60JTtsQmyQerLQFFYdqYN9pYpi00weG1Vox4"|| 'pk_test_demo_key';

// Stripe appearance options
export const STRIPE_APPEARANCE = {
  theme: 'stripe',
  variables: {
    colorPrimary: '#575CEE',
    colorBackground: '#ffffff',
    colorText: '#30313d',
    colorDanger: '#df1b41',
    fontFamily: 'system-ui, sans-serif',
    spacingUnit: '4px',
    borderRadius: '6px',
  },
};

// Card element options
export const CARD_ELEMENT_OPTIONS = {
  style: {
    base: {
      fontSize: '16px',
      color: '#424770',
      '::placeholder': {
        color: '#aab7c4',
      },
    },
    invalid: {
      color: '#9e2146',
    },
  },
  hidePostalCode: false,
};

// Payment method types
export const PAYMENT_METHOD_TYPES = ['card'];

// Currency
export const CURRENCY = 'usd';

// Demo mode flag
export const IS_DEMO_MODE = process.env.REACT_APP_STRIPE_DEMO_MODE === 'true' || false;

// API Configuration
export const API_CONFIG = {
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000/api',
  timeout: 30000, // 30 seconds
  endpoints: {
    createPaymentIntent: '/payments/create-payment-intent',
    confirmPayment: '/payments/confirm-payment',
    paymentStatus: '/payments/status',
    createOrder: '/orders',
    updateOrder: '/orders',
    sendEmail: '/emails/order-confirmation',
    webhook: '/webhooks/stripe'
  }
};
