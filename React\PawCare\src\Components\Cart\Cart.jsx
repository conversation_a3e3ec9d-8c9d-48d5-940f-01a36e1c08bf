import React from 'react';
import { Link } from 'react-router-dom';
import { FaTrash, FaArrowLeft, FaPlus, FaMinus } from 'react-icons/fa';
import { useCart } from '../../context/CartContext';

const Cart = () => {
  const { cartItems, cartTotal, removeFromCart, updateQuantity, clearCart } = useCart();

  if (cartItems.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-center mb-6">Your Cart</h1>
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">Your cart is empty</p>
            <Link 
              to="/home" 
              className="inline-flex items-center px-4 py-2 bg-[#575CEE] text-white rounded-md hover:bg-[#4a4fd1]"
            >
              <FaArrowLeft className="mr-2" /> Continue Shopping
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Your Cart</h1>
          <button 
            onClick={clearCart}
            className="text-red-500 hover:text-red-700 flex items-center"
          >
            <FaTrash className="mr-1" /> Clear Cart
          </button>
        </div>

        <div className="border-t border-gray-200 pt-4">
          {cartItems.map((item) => (
            <div key={item.id} className="flex flex-col sm:flex-row items-center py-4 border-b border-gray-200">
              <div className="w-24 h-24 flex-shrink-0 bg-gray-100 rounded-md overflow-hidden mr-4 mb-4 sm:mb-0">
                <img 
                  src={item.img} 
                  alt={item.title} 
                  className="w-full h-full object-contain p-2"
                />
              </div>
              
              <div className="flex-grow">
                <h3 className="font-medium text-gray-900">{item.title}</h3>
                <p className="text-gray-500 text-sm">{item.description}</p>
                <p className="text-[#575CEE] font-bold mt-1">{item.price}</p>
              </div>
              
              <div className="flex items-center mt-4 sm:mt-0">
                <button 
                  onClick={() => updateQuantity(item.id, item.quantity - 1)}
                  className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                >
                  <FaMinus className="text-gray-600" />
                </button>
                
                <span className="mx-3 w-8 text-center">{item.quantity}</span>
                
                <button 
                  onClick={() => updateQuantity(item.id, item.quantity + 1)}
                  className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                >
                  <FaPlus className="text-gray-600" />
                </button>
                
                <button 
                  onClick={() => removeFromCart(item.id)}
                  className="ml-4 text-red-500 hover:text-red-700"
                >
                  <FaTrash />
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 border-t border-gray-200 pt-4">
          <div className="flex justify-between text-lg font-medium">
            <span>Subtotal</span>
            <span>${cartTotal.toFixed(2)}</span>
          </div>
          <p className="text-gray-500 text-sm mt-1">Shipping and taxes calculated at checkout</p>
          
          <div className="mt-6 space-y-3">
            <button 
              className="w-full bg-[#575CEE] text-white py-3 px-4 rounded-md hover:bg-[#4a4fd1] transition-colors"
            >
              Checkout
            </button>
            
            <Link 
              to="/home" 
              className="w-full block text-center border border-gray-300 text-gray-700 py-3 px-4 rounded-md hover:bg-gray-50 transition-colors"
            >
              Continue Shopping
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cart;
