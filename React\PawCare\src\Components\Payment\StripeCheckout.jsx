import React, { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { FaCreditCard, FaLock, FaShoppingCart, FaUser, FaEnvelope, FaMapMarkerAlt } from 'react-icons/fa';
import { useCart } from '../../context/CartContext';
import { STRIPE_PUBLISHABLE_KEY, CARD_ELEMENT_OPTIONS } from '../../config/stripe';
import { createPaymentIntent, confirmPayment, createOrder, sendOrderConfirmation } from '../../services/paymentService';

// Initialize Stripe
const stripePromise = loadStripe(STRIPE_PUBLISHABLE_KEY);

// Checkout Form Component
const CheckoutForm = ({ onClose, onSuccess }) => {
  const { cartItems, cartTotal, clearCart } = useCart();
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentData, setPaymentData] = useState({
    email: '',
    fullName: '',
    address: '',
    city: '',
    zipCode: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setPaymentData(prev => ({
      ...prev,
      [name]: value
    }));
  };



  const validateForm = () => {
    const requiredFields = ['email', 'fullName', 'address', 'city', 'zipCode'];
    const missingFields = requiredFields.filter(field => !paymentData[field].trim());

    if (missingFields.length > 0) {
      alert('Please fill in all required fields.');
      return false;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(paymentData.email)) {
      alert('Please enter a valid email address.');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!stripe || !elements) {
      alert('Stripe has not loaded yet. Please try again.');
      return;
    }

    if (!validateForm()) {
      return;
    }

    setIsProcessing(true);

    try {
      // Step 1: Create payment intent on backend
      const orderId = `order_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

      const paymentIntentData = {
        amount: Math.round(cartTotal * 100), // Convert to cents
        currency: 'usd',
        items: cartItems.map(item => ({
          id: item.id,
          title: item.title,
          price: parseFloat(item.price.replace('$', '')),
          quantity: item.quantity,
          total: parseFloat(item.price.replace('$', '')) * item.quantity
        })),
        email: paymentData.email,
        fullName: paymentData.fullName,
        address: paymentData.address,
        city: paymentData.city,
        zipCode: paymentData.zipCode,
        orderId: orderId
      };

      console.log('Creating payment intent...', paymentIntentData);
      const { clientSecret, paymentIntentId } = await createPaymentIntent(paymentIntentData);

      // Step 2: Create payment method
      const cardElement = elements.getElement(CardElement);

      const { error: paymentMethodError, paymentMethod } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
        billing_details: {
          name: paymentData.fullName,
          email: paymentData.email,
          address: {
            line1: paymentData.address,
            city: paymentData.city,
            postal_code: paymentData.zipCode,
          },
        },
      });

      if (paymentMethodError) {
        console.error('Payment method creation failed:', paymentMethodError);
        alert(`Payment failed: ${paymentMethodError.message}`);
        return;
      }

      // Step 3: Confirm payment with Stripe
      const { error: confirmError, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: paymentMethod.id
      });

      if (confirmError) {
        console.error('Payment confirmation failed:', confirmError);
        alert(`Payment failed: ${confirmError.message}`);
        return;
      }

      // Step 4: Confirm payment on backend
      console.log('Confirming payment on backend...');
      const confirmationResult = await confirmPayment({
        paymentIntentId: paymentIntent.id,
        paymentMethodId: paymentMethod.id,
        orderId: orderId
      });

      // Step 5: Create order in backend
      console.log('Creating order...');
      const orderData = {
        items: cartItems.map(item => ({
          id: item.id,
          title: item.title,
          price: parseFloat(item.price.replace('$', '')),
          quantity: item.quantity,
          image: item.img
        })),
        total: cartTotal,
        customer: {
          name: paymentData.fullName,
          email: paymentData.email,
        },
        shippingAddress: {
          line1: paymentData.address,
          city: paymentData.city,
          postal_code: paymentData.zipCode,
        },
        paymentIntentId: paymentIntent.id,
        orderId: orderId
      };

      const order = await createOrder(orderData);

      // Step 6: Send confirmation email (optional, don't fail if this fails)
      try {
        await sendOrderConfirmation({
          email: paymentData.email,
          customerName: paymentData.fullName,
          orderDetails: {
            orderId: orderId,
            items: cartItems,
            total: cartTotal,
            paymentIntentId: paymentIntent.id
          }
        });
      } catch (emailError) {
        console.warn('Failed to send confirmation email:', emailError);
      }

      // Step 7: Success handling
      const paymentResult = {
        success: true,
        transactionId: paymentIntent.id,
        orderId: orderId,
        amount: cartTotal,
        items: cartItems,
        paymentMethodId: paymentMethod.id,
        order: order
      };

      // Clear the cart after successful payment
      clearCart();

      // Show success message
      alert(`🎉 Payment Successful!\n\nOrder ID: ${orderId}\nTransaction ID: ${paymentIntent.id}\nAmount: $${cartTotal.toFixed(2)}\n\nThank you for your purchase! A confirmation email will be sent to ${paymentData.email}`);

      // Call success callback
      if (onSuccess) {
        onSuccess(paymentResult);
      }

      // Close the checkout
      if (onClose) {
        onClose();
      }

    } catch (error) {
      console.error('Payment error:', error);

      // Show user-friendly error message
      let errorMessage = 'Payment failed. Please try again.';

      if (error.message.includes('network') || error.message.includes('fetch')) {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (error.message.includes('card')) {
        errorMessage = 'Card error. Please check your card details and try again.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert(`Payment Failed: ${errorMessage}`);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-[#575CEE] text-white p-4 rounded-t-lg">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold flex items-center">
              <FaShoppingCart className="mr-2" />
              Secure Checkout
            </h2>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 text-xl"
            >
              ×
            </button>
          </div>
          <div className="mt-2 flex items-center text-sm">
            <FaLock className="mr-1" />
            SSL Secured Payment
          </div>
        </div>

        {/* Order Summary */}
        <div className="p-4 bg-gray-50 border-b">
          <h3 className="font-semibold mb-2">Order Summary</h3>
          <div className="space-y-1 text-sm">
            {cartItems.map((item) => (
              <div key={item.id} className="flex justify-between">
                <span>{item.title} × {item.quantity}</span>
                <span>${(parseFloat(item.price.replace('$', '')) * item.quantity).toFixed(2)}</span>
              </div>
            ))}
            <div className="border-t pt-1 font-semibold flex justify-between">
              <span>Total:</span>
              <span>${cartTotal.toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* Payment Form */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* Contact Information */}
          <div>
            <h3 className="font-semibold mb-3 flex items-center">
              <FaUser className="mr-2" />
              Contact Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FaEnvelope className="inline mr-1" />
                  Email Address *
                </label>
                <input
                  type="email"
                  name="email"
                  value={paymentData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#575CEE]"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                <input
                  type="text"
                  name="fullName"
                  value={paymentData.fullName}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#575CEE]"
                  placeholder="John Doe"
                />
              </div>
            </div>
          </div>

          {/* Shipping Address */}
          <div>
            <h3 className="font-semibold mb-3 flex items-center">
              <FaMapMarkerAlt className="mr-2" />
              Shipping Address
            </h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Address *</label>
                <input
                  type="text"
                  name="address"
                  value={paymentData.address}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#575CEE]"
                  placeholder="123 Main Street"
                />
              </div>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">City *</label>
                  <input
                    type="text"
                    name="city"
                    value={paymentData.city}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#575CEE]"
                    placeholder="New York"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ZIP Code *</label>
                  <input
                    type="text"
                    name="zipCode"
                    value={paymentData.zipCode}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#575CEE]"
                    placeholder="10001"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Payment Information */}
          <div>
            <h3 className="font-semibold mb-3 flex items-center">
              <FaCreditCard className="mr-2" />
              Payment Information
            </h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Card Details *</label>
                <div className="w-full px-3 py-3 border border-gray-300 rounded-md focus-within:ring-2 focus-within:ring-[#575CEE] focus-within:border-transparent">
                  <CardElement options={CARD_ELEMENT_OPTIONS} />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Enter your card number, expiry date, and CVC
                </p>
              </div>
            </div>
          </div>

          {/* Security Notice */}
          <div className="bg-green-50 p-3 rounded-md">
            <p className="text-sm text-green-800 flex items-center">
              <FaLock className="mr-2" />
              Your payment information is secure and encrypted. We never store your card details.
            </p>
          </div>

          {/* Submit Button */}
          <div className="pt-4">
            <button
              type="submit"
              disabled={isProcessing}
              className={`w-full py-3 px-4 rounded-md font-semibold flex items-center justify-center ${
                isProcessing
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-[#575CEE] hover:bg-[#4a4fd1] text-white'
              }`}
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Processing Payment...
                </>
              ) : (
                <>
                  <FaLock className="mr-2" />
                  Pay ${cartTotal.toFixed(2)}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Main StripeCheckout component that wraps CheckoutForm with Elements provider
const StripeCheckout = ({ onClose, onSuccess }) => {
  return (
    <Elements stripe={stripePromise}>
      <CheckoutForm onClose={onClose} onSuccess={onSuccess} />
    </Elements>
  );
};

export default StripeCheckout;
