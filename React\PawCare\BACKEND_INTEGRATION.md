# Backend Stripe Integration Guide

This guide explains how to connect your PawCare frontend with your backend Stripe payment processing.

## 🚀 Quick Start

The frontend is now **ready to connect** to your backend! It includes:
- ✅ Full Stripe payment integration
- ✅ Automatic fallback to demo mode
- ✅ Comprehensive error handling
- ✅ Backend API integration
- ✅ Order management system

## 🎯 Current Frontend Setup

Your frontend now includes these components:

### Payment Integration
- **StripeCheckout.jsx** - Complete checkout form with Stripe Elements
- **paymentService.js** - API service with backend integration + demo fallback
- **demoPaymentService.js** - Demo mode for development/testing
- **stripe.js** - Configuration file for Stripe settings

### Features Working Now
1. **Cart to Checkout Flow** - Users can proceed from cart to payment
2. **Stripe Payment Form** - Secure card input with validation
3. **Backend Integration** - Calls your API endpoints (with fallback)
4. **Demo Mode** - Works without backend for testing
5. **Error Handling** - Graceful fallbacks and user-friendly messages

### Environment Setup
Create a `.env` file with:
```env
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here
REACT_APP_API_BASE_URL=http://localhost:5000/api
REACT_APP_STRIPE_DEMO_MODE=true
```

## Overview

The frontend now makes the following API calls to your backend:

1. **Create Payment Intent** - Creates a Stripe payment intent
2. **Confirm Payment** - Confirms the payment was successful
3. **Create Order** - Stores order details in your database
4. **Send Confirmation Email** - Sends order confirmation to customer

## Required Backend Endpoints

### 1. POST `/api/payments/create-payment-intent`

Creates a Stripe payment intent on your backend.

**Request Body:**
```json
{
  "amount": 2999,
  "currency": "usd",
  "items": [
    {
      "id": "1",
      "title": "Dog Food",
      "price": 29.99,
      "quantity": 1,
      "total": 29.99
    }
  ],
  "email": "<EMAIL>",
  "fullName": "John Doe",
  "address": "123 Main St",
  "city": "New York",
  "zipCode": "10001",
  "orderId": "order_1234567890_abc123"
}
```

**Response:**
```json
{
  "clientSecret": "pi_1234567890_secret_abc123",
  "paymentIntentId": "pi_1234567890"
}
```

### 2. POST `/api/payments/confirm-payment`

Confirms payment was successful and updates records.

**Request Body:**
```json
{
  "paymentIntentId": "pi_1234567890",
  "paymentMethodId": "pm_1234567890",
  "orderId": "order_1234567890_abc123"
}
```

**Response:**
```json
{
  "success": true,
  "paymentStatus": "succeeded",
  "message": "Payment confirmed successfully"
}
```

### 3. POST `/api/orders`

Creates an order record in your database.

**Request Body:**
```json
{
  "items": [...],
  "total": 29.99,
  "customer": {
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "shippingAddress": {
    "line1": "123 Main St",
    "city": "New York",
    "postal_code": "10001"
  },
  "paymentIntentId": "pi_1234567890",
  "orderId": "order_1234567890_abc123"
}
```

**Response:**
```json
{
  "id": "order_db_id_123",
  "orderId": "order_1234567890_abc123",
  "status": "pending",
  "createdAt": "2024-01-01T00:00:00Z"
}
```

### 4. POST `/api/emails/order-confirmation`

Sends order confirmation email to customer.

**Request Body:**
```json
{
  "to": "<EMAIL>",
  "customerName": "John Doe",
  "orderDetails": {
    "orderId": "order_1234567890_abc123",
    "items": [...],
    "total": 29.99,
    "paymentIntentId": "pi_1234567890"
  }
}
```

## Environment Variables

Add these to your backend `.env` file:

```env
# Stripe Keys
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Database
DATABASE_URL=your_database_connection_string

# Email Service (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# CORS
FRONTEND_URL=http://localhost:3000
```

## Example Backend Implementation (Node.js/Express)

### Install Dependencies

```bash
npm install stripe express cors dotenv nodemailer
```

### Basic Server Setup

```javascript
// server.js
const express = require('express');
const cors = require('cors');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
require('dotenv').config();

const app = express();

app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000'
}));
app.use(express.json());

// Routes
app.use('/api/payments', require('./routes/payments'));
app.use('/api/orders', require('./routes/orders'));
app.use('/api/emails', require('./routes/emails'));

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
```

### Payment Routes

```javascript
// routes/payments.js
const express = require('express');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const router = express.Router();

// Create Payment Intent
router.post('/create-payment-intent', async (req, res) => {
  try {
    const { amount, currency, items, email, fullName, orderId } = req.body;

    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      metadata: {
        orderId,
        customerEmail: email,
        customerName: fullName,
        items: JSON.stringify(items)
      }
    });

    res.json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    res.status(500).json({ error: error.message });
  }
});

// Confirm Payment
router.post('/confirm-payment', async (req, res) => {
  try {
    const { paymentIntentId } = req.body;

    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status === 'succeeded') {
      // Update your database here
      res.json({
        success: true,
        paymentStatus: paymentIntent.status,
        message: 'Payment confirmed successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Payment not successful'
      });
    }
  } catch (error) {
    console.error('Error confirming payment:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
```

### Order Routes

```javascript
// routes/orders.js
const express = require('express');
const router = express.Router();

// Create Order
router.post('/', async (req, res) => {
  try {
    const orderData = req.body;

    // Save to your database
    // const order = await Order.create(orderData);

    // For demo, return mock response
    const order = {
      id: `db_${Date.now()}`,
      orderId: orderData.orderId,
      status: 'pending',
      createdAt: new Date().toISOString()
    };

    res.json(order);
  } catch (error) {
    console.error('Error creating order:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
```

## Webhook Setup (Recommended)

Set up webhooks to handle payment events securely:

```javascript
// routes/webhooks.js
const express = require('express');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const router = express.Router();

router.post('/stripe', express.raw({type: 'application/json'}), (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    console.log(`Webhook signature verification failed.`, err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      console.log('Payment succeeded:', paymentIntent.id);
      // Update order status in database
      break;
    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object;
      console.log('Payment failed:', failedPayment.id);
      // Handle failed payment
      break;
    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.json({received: true});
});

module.exports = router;
```

## Testing

1. Start your backend server
2. Update `REACT_APP_API_BASE_URL` in frontend `.env`
3. Test with Stripe test cards:
   - Success: 4242 4242 4242 4242
   - Decline: 4000 0000 0000 0002

## Security Considerations

- Never expose your Stripe secret key in frontend
- Validate all inputs on backend
- Use HTTPS in production
- Implement rate limiting
- Set up proper CORS policies
- Use webhooks for critical payment events

## Next Steps

1. Set up your database models for orders
2. Implement email service for confirmations
3. Add webhook endpoints for payment events
4. Set up proper error logging
5. Implement order management system
