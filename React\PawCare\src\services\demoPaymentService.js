// Demo Payment Service - Fallback when backend is not available
import { IS_DEMO_MODE } from '../config/stripe';

/**
 * Demo implementation of payment services for development/testing
 */

export const createPaymentIntentDemo = async (paymentData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  console.log('Demo Mode: Creating payment intent', paymentData);
  
  // Simulate payment intent creation
  const paymentIntentId = `pi_demo_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  const clientSecret = `${paymentIntentId}_secret_demo`;
  
  return {
    clientSecret,
    paymentIntentId,
    demo: true
  };
};

export const confirmPaymentDemo = async (confirmationData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  console.log('Demo Mode: Confirming payment', confirmationData);
  
  return {
    success: true,
    paymentStatus: 'succeeded',
    message: 'Payment confirmed successfully (Demo Mode)',
    demo: true
  };
};

export const createOrderDemo = async (orderData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));
  
  console.log('Demo Mode: Creating order', orderData);
  
  const order = {
    id: `demo_order_${Date.now()}`,
    orderId: orderData.orderId,
    status: 'pending',
    createdAt: new Date().toISOString(),
    demo: true,
    ...orderData
  };
  
  return order;
};

export const sendOrderConfirmationDemo = async (emailData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 400));
  
  console.log('Demo Mode: Sending confirmation email', emailData);
  
  return {
    success: true,
    message: 'Confirmation email sent (Demo Mode)',
    demo: true
  };
};

/**
 * Check if we should use demo mode
 */
export const shouldUseDemoMode = () => {
  return IS_DEMO_MODE || process.env.NODE_ENV === 'development';
};

/**
 * Demo error simulation for testing error handling
 */
export const simulateError = (errorType = 'network') => {
  const errors = {
    network: new Error('Network error: Unable to connect to payment server'),
    card: new Error('Your card was declined. Please try a different payment method.'),
    server: new Error('Server error: Payment processing temporarily unavailable'),
    validation: new Error('Invalid payment data provided')
  };
  
  return errors[errorType] || errors.network;
};

/**
 * Demo payment flow with optional error simulation
 */
export const demoPaymentFlow = async (paymentData, simulateErrorType = null) => {
  try {
    if (simulateErrorType) {
      throw simulateError(simulateErrorType);
    }
    
    console.log('🎭 Demo Payment Flow Started');
    
    // Step 1: Create payment intent
    const paymentIntent = await createPaymentIntentDemo(paymentData);
    console.log('✅ Payment intent created (demo)');
    
    // Step 2: Confirm payment
    const confirmation = await confirmPaymentDemo({
      paymentIntentId: paymentIntent.paymentIntentId,
      paymentMethodId: 'pm_demo_123',
      orderId: paymentData.orderId
    });
    console.log('✅ Payment confirmed (demo)');
    
    // Step 3: Create order
    const order = await createOrderDemo({
      ...paymentData,
      paymentIntentId: paymentIntent.paymentIntentId
    });
    console.log('✅ Order created (demo)');
    
    // Step 4: Send email
    const emailResult = await sendOrderConfirmationDemo({
      email: paymentData.email,
      customerName: paymentData.fullName,
      orderDetails: {
        orderId: paymentData.orderId,
        items: paymentData.items,
        total: paymentData.amount / 100,
        paymentIntentId: paymentIntent.paymentIntentId
      }
    });
    console.log('✅ Email sent (demo)');
    
    console.log('🎉 Demo Payment Flow Completed Successfully');
    
    return {
      success: true,
      paymentIntent,
      confirmation,
      order,
      emailResult,
      demo: true
    };
    
  } catch (error) {
    console.error('❌ Demo Payment Flow Failed:', error.message);
    throw error;
  }
};

export default {
  createPaymentIntentDemo,
  confirmPaymentDemo,
  createOrderDemo,
  sendOrderConfirmationDemo,
  shouldUseDemoMode,
  simulateError,
  demoPaymentFlow
};
