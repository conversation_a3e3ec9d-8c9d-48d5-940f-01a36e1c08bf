import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "../ui/animated-modal";
import { FaHeart, FaDollarSign, FaUser, FaEnvelope, FaCreditCard, FaCalendarAlt, FaLock } from 'react-icons/fa';

const DonationModal = () => {
  const [selectedAmount, setSelectedAmount] = useState('');
  const [customAmount, setCustomAmount] = useState('');
  const [donorInfo, setDonorInfo] = useState({
    name: '',
    email: '',
    cardNumber: '',
    expiryDate: '',
    cvv: ''
  });

  const presetAmounts = [25, 50, 100, 250, 500];

  const handleAmountSelect = (amount) => {
    setSelectedAmount(amount);
    setCustomAmount('');
  };

  const handleCustomAmountChange = (e) => {
    const value = e.target.value;
    setCustomAmount(value);
    setSelectedAmount('');
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setDonorInfo(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDonate = (e) => {
    e.preventDefault();
    const donationAmount = selectedAmount || customAmount;
    
    if (!donationAmount || donationAmount <= 0) {
      alert('Please select or enter a valid donation amount.');
      return;
    }

    // Handle donation submission here
    console.log('Donation submitted:', {
      amount: donationAmount,
      donor: donorInfo
    });
    
    alert(`Thank you for your generous donation of $${donationAmount}! Your support helps us care for animals in need.`);
    
    // Reset form
    setSelectedAmount('');
    setCustomAmount('');
    setDonorInfo({
      name: '',
      email: '',
      cardNumber: '',
      expiryDate: '',
      cvv: ''
    });
  };

  return (
    <Modal>
      <ModalTrigger className="bg-white text-blue-500 border-2 border-blue-500 px-6 py-3 rounded-full hover:scale-105 transition duration-300 transform hover:shadow-lg shadow-blue-100 font-medium flex items-center">
        <FaHeart className="mr-2" /> Donate Now
      </ModalTrigger>
      <ModalBody>
        <ModalContent>
          <div className="text-center mb-6">
            <div className="bg-red-100 p-4 rounded-full text-red-600 mb-4 w-16 h-16 flex items-center justify-center mx-auto">
              <FaHeart size={24} />
            </div>
            <h4 className="text-2xl font-bold text-gray-800 mb-2">
              Make a Donation
            </h4>
            <p className="text-gray-600">
              Your donation helps us provide food, medical care, and shelter for animals in need.
            </p>
          </div>

          <form onSubmit={handleDonate} className="space-y-6">
            {/* Donation Amount Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                <FaDollarSign className="inline mr-2" />
                Select Donation Amount
              </label>
              <div className="grid grid-cols-3 gap-2 mb-3">
                {presetAmounts.map((amount) => (
                  <button
                    key={amount}
                    type="button"
                    onClick={() => handleAmountSelect(amount)}
                    className={`py-2 px-4 rounded-lg border-2 transition-colors duration-200 ${
                      selectedAmount === amount
                        ? 'border-blue-500 bg-blue-50 text-blue-600'
                        : 'border-gray-300 hover:border-blue-300'
                    }`}
                  >
                    ${amount}
                  </button>
                ))}
              </div>
              <input
                type="number"
                value={customAmount}
                onChange={handleCustomAmountChange}
                placeholder="Enter custom amount"
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Donor Information */}
            <div className="space-y-4">
              <h5 className="text-lg font-semibold text-gray-800">Donor Information</h5>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FaUser className="inline mr-2" />
                  Full Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={donorInfo.name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter your full name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FaEnvelope className="inline mr-2" />
                  Email Address
                </label>
                <input
                  type="email"
                  name="email"
                  value={donorInfo.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter your email address"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FaCreditCard className="inline mr-2" />
                  Card Number
                </label>
                <input
                  type="text"
                  name="cardNumber"
                  value={donorInfo.cardNumber}
                  onChange={handleInputChange}
                  required
                  placeholder="1234 5678 9012 3456"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <FaCalendarAlt className="inline mr-2" />
                    Expiry Date
                  </label>
                  <input
                    type="text"
                    name="expiryDate"
                    value={donorInfo.expiryDate}
                    onChange={handleInputChange}
                    required
                    placeholder="MM/YY"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <FaLock className="inline mr-2" />
                    CVV
                  </label>
                  <input
                    type="text"
                    name="cvv"
                    value={donorInfo.cvv}
                    onChange={handleInputChange}
                    required
                    placeholder="123"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-sm text-blue-800">
                <FaLock className="inline mr-2" />
                Your payment information is secure and encrypted. We never store your card details.
              </p>
            </div>
          </form>
        </ModalContent>
        <ModalFooter className="gap-4">
          <button 
            type="button"
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors duration-200"
          >
            Cancel
          </button>
          <button 
            type="submit"
            onClick={handleDonate}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200 flex items-center"
          >
            <FaHeart className="mr-2" />
            Donate ${selectedAmount || customAmount || '0'}
          </button>
        </ModalFooter>
      </ModalBody>
    </Modal>
  );
};

export default DonationModal;
