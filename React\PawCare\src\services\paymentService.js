import axios from 'axios';
import {
  createPaymentIntentDemo,
  confirmPaymentDemo,
  createOrderDemo,
  sendOrderConfirmationDemo,
  shouldUseDemoMode
} from './demoPaymentService';

// Base URL for your backend API
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token if available
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

/**
 * Create a payment intent on the backend
 * @param {Object} paymentData - Payment and order information
 * @returns {Promise} - Payment intent from backend
 */
export const createPaymentIntent = async (paymentData) => {
  // Use demo mode if enabled or backend is unavailable
  if (shouldUseDemoMode()) {
    console.log('Using demo mode for payment intent creation');
    return await createPaymentIntentDemo(paymentData);
  }

  try {
    const response = await api.post('/payments/create-payment-intent', {
      amount: paymentData.amount,
      currency: paymentData.currency || 'usd',
      items: paymentData.items,
      customer: {
        email: paymentData.email,
        name: paymentData.fullName,
        address: {
          line1: paymentData.address,
          city: paymentData.city,
          postal_code: paymentData.zipCode,
        },
      },
      metadata: {
        orderId: paymentData.orderId || `order_${Date.now()}`,
        source: 'pawcare_web',
      },
    });

    return response.data;
  } catch (error) {
    console.warn('Backend payment intent creation failed, falling back to demo mode:', error.message);
    return await createPaymentIntentDemo(paymentData);
  }
};

/**
 * Confirm payment on the backend
 * @param {Object} confirmationData - Payment confirmation data
 * @returns {Promise} - Confirmation result
 */
export const confirmPayment = async (confirmationData) => {
  // Use demo mode if enabled or backend is unavailable
  if (shouldUseDemoMode()) {
    console.log('Using demo mode for payment confirmation');
    return await confirmPaymentDemo(confirmationData);
  }

  try {
    const response = await api.post('/payments/confirm-payment', {
      paymentIntentId: confirmationData.paymentIntentId,
      paymentMethodId: confirmationData.paymentMethodId,
      orderId: confirmationData.orderId,
    });

    return response.data;
  } catch (error) {
    console.warn('Backend payment confirmation failed, falling back to demo mode:', error.message);
    return await confirmPaymentDemo(confirmationData);
  }
};

/**
 * Get payment status from backend
 * @param {string} paymentIntentId - Payment intent ID
 * @returns {Promise} - Payment status
 */
export const getPaymentStatus = async (paymentIntentId) => {
  try {
    const response = await api.get(`/payments/status/${paymentIntentId}`);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to get payment status');
  }
};

/**
 * Create order in backend
 * @param {Object} orderData - Order information
 * @returns {Promise} - Created order
 */
export const createOrder = async (orderData) => {
  // Use demo mode if enabled or backend is unavailable
  if (shouldUseDemoMode()) {
    console.log('Using demo mode for order creation');
    return await createOrderDemo(orderData);
  }

  try {
    const response = await api.post('/orders', {
      items: orderData.items,
      total: orderData.total,
      customer: orderData.customer,
      shippingAddress: orderData.shippingAddress,
      paymentIntentId: orderData.paymentIntentId,
      orderId: orderData.orderId,
      status: 'pending',
    });

    return response.data;
  } catch (error) {
    console.warn('Backend order creation failed, falling back to demo mode:', error.message);
    return await createOrderDemo(orderData);
  }
};

/**
 * Update order status
 * @param {string} orderId - Order ID
 * @param {string} status - New status
 * @returns {Promise} - Updated order
 */
export const updateOrderStatus = async (orderId, status) => {
  try {
    const response = await api.patch(`/orders/${orderId}/status`, { status });
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to update order status');
  }
};

/**
 * Send order confirmation email
 * @param {Object} emailData - Email data
 * @returns {Promise} - Email send result
 */
export const sendOrderConfirmation = async (emailData) => {
  // Use demo mode if enabled or backend is unavailable
  if (shouldUseDemoMode()) {
    console.log('Using demo mode for email confirmation');
    return await sendOrderConfirmationDemo(emailData);
  }

  try {
    const response = await api.post('/emails/order-confirmation', {
      to: emailData.email,
      orderDetails: emailData.orderDetails,
      customerName: emailData.customerName,
    });

    return response.data;
  } catch (error) {
    console.warn('Backend email sending failed, falling back to demo mode:', error.message);
    // Don't throw error for email failures as payment was successful
    return await sendOrderConfirmationDemo(emailData);
  }
};

/**
 * Handle webhook events (for testing purposes)
 * @param {Object} webhookData - Webhook event data
 * @returns {Promise} - Webhook processing result
 */
export const processWebhook = async (webhookData) => {
  try {
    const response = await api.post('/webhooks/stripe', webhookData);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to process webhook');
  }
};

export default {
  createPaymentIntent,
  confirmPayment,
  getPaymentStatus,
  createOrder,
  updateOrderStatus,
  sendOrderConfirmation,
  processWebhook,
};
