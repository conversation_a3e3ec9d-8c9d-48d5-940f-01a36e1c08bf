import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "../ui/animated-modal";
import { FaCalendarAlt, FaClock, FaUser, FaEnvelope, FaPhone, FaPaw, FaStethoscope, FaNotesMedical } from 'react-icons/fa';

const AppointmentModal = ({ triggerButton, clinicName = "PawCare Veterinary Clinic" }) => {
  const [appointmentData, setAppointmentData] = useState({
    petName: '',
    petType: '',
    ownerName: '',
    email: '',
    phone: '',
    appointmentDate: '',
    appointmentTime: '',
    serviceType: '',
    notes: ''
  });

  const serviceTypes = [
    'Wellness Exam',
    'Vaccination',
    'Surgery',
    'Dental Care',
    'Emergency Care',
    'Grooming',
    'Behavioral Consultation',
    'Other'
  ];

  const timeSlots = [
    '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM',
    '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM'
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setAppointmentData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle appointment booking submission here
    console.log('Appointment booked:', appointmentData);
    alert(`Appointment booked successfully at ${clinicName}!\n\nDetails:\nPet: ${appointmentData.petName}\nDate: ${appointmentData.appointmentDate}\nTime: ${appointmentData.appointmentTime}\nService: ${appointmentData.serviceType}\n\nWe'll send a confirmation email to ${appointmentData.email}`);
    
    // Reset form
    setAppointmentData({
      petName: '',
      petType: '',
      ownerName: '',
      email: '',
      phone: '',
      appointmentDate: '',
      appointmentTime: '',
      serviceType: '',
      notes: ''
    });
  };

  // Get tomorrow's date as minimum date
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const minDate = tomorrow.toISOString().split('T')[0];

  return (
    <Modal>
      <ModalTrigger className={triggerButton?.className || "bg-indigo-500 text-white py-1.5 px-4 rounded-full text-sm hover:bg-indigo-600 transition"}>
        {triggerButton?.text || "Book an Appointment"}
      </ModalTrigger>
      <ModalBody>
        <ModalContent className="max-h-[85vh] overflow-y-auto">
          <div className="text-center mb-4">
            <div className="bg-green-100 p-3 rounded-full text-green-600 mb-3 w-12 h-12 flex items-center justify-center mx-auto">
              <FaStethoscope size={20} />
            </div>
            <h4 className="text-xl font-bold text-gray-800 mb-1">
              Book Appointment
            </h4>
            <p className="text-gray-600 text-sm">
              Schedule an appointment at {clinicName}
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-3">
            {/* Pet Information */}
            <div className="bg-gray-50 p-3 rounded-md">
              <h5 className="text-sm font-semibold text-gray-800 mb-2 flex items-center">
                <FaPaw className="mr-1" /> Pet Information
              </h5>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Pet Name</label>
                  <input
                    type="text"
                    name="petName"
                    value={appointmentData.petName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500"
                    placeholder="e.g., Max"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Pet Type</label>
                  <select
                    name="petType"
                    value={appointmentData.petType}
                    onChange={handleInputChange}
                    required
                    className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500"
                  >
                    <option value="">Select type</option>
                    <option value="Dog">Dog</option>
                    <option value="Cat">Cat</option>
                    <option value="Bird">Bird</option>
                    <option value="Rabbit">Rabbit</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Owner Information */}
            <div className="bg-gray-50 p-3 rounded-md">
              <h5 className="text-sm font-semibold text-gray-800 mb-2 flex items-center">
                <FaUser className="mr-1" /> Owner Information
              </h5>
              
              <div className="space-y-2">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Full Name</label>
                  <input
                    type="text"
                    name="ownerName"
                    value={appointmentData.ownerName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500"
                    placeholder="Enter your full name"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      <FaEnvelope className="inline mr-1" />Email
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={appointmentData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      <FaPhone className="inline mr-1" />Phone
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={appointmentData.phone}
                      onChange={handleInputChange}
                      required
                      className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500"
                      placeholder="(*************"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Appointment Details */}
            <div className="bg-gray-50 p-3 rounded-md">
              <h5 className="text-sm font-semibold text-gray-800 mb-2 flex items-center">
                <FaCalendarAlt className="mr-1" /> Appointment Details
              </h5>
              
              <div className="space-y-2">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Date</label>
                    <input
                      type="date"
                      name="appointmentDate"
                      value={appointmentData.appointmentDate}
                      onChange={handleInputChange}
                      required
                      min={minDate}
                      className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500"
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      <FaClock className="inline mr-1" />Time
                    </label>
                    <select
                      name="appointmentTime"
                      value={appointmentData.appointmentTime}
                      onChange={handleInputChange}
                      required
                      className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500"
                    >
                      <option value="">Select time</option>
                      {timeSlots.map((time) => (
                        <option key={time} value={time}>{time}</option>
                      ))}
                    </select>
                  </div>
                </div>
                
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    <FaNotesMedical className="inline mr-1" />Service Type
                  </label>
                  <select
                    name="serviceType"
                    value={appointmentData.serviceType}
                    onChange={handleInputChange}
                    required
                    className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500"
                  >
                    <option value="">Select service</option>
                    {serviceTypes.map((service) => (
                      <option key={service} value={service}>{service}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Additional Notes</label>
                  <textarea
                    name="notes"
                    value={appointmentData.notes}
                    onChange={handleInputChange}
                    rows="2"
                    className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500 resize-none"
                    placeholder="Any special concerns or requests..."
                  />
                </div>
              </div>
            </div>
          </form>
        </ModalContent>
        <ModalFooter className="gap-3 p-3">
          <button 
            type="button"
            className="px-3 py-1.5 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors duration-200"
          >
            Cancel
          </button>
          <button 
            type="submit"
            onClick={handleSubmit}
            className="px-3 py-1.5 text-sm bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors duration-200 flex items-center"
          >
            <FaCalendarAlt className="mr-1" />
            Book Appointment
          </button>
        </ModalFooter>
      </ModalBody>
    </Modal>
  );
};

export default AppointmentModal;
