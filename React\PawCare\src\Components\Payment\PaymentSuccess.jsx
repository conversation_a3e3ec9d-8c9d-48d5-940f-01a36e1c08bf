import React from 'react';
import { Link } from 'react-router-dom';
import { FaCheckCircle, FaShoppingBag, FaHome, FaEnvelope } from 'react-icons/fa';

const PaymentSuccess = ({ transactionId, amount, items }) => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8 text-center">
        {/* Success Icon */}
        <div className="mb-6">
          <FaCheckCircle className="text-green-500 text-6xl mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Payment Successful!</h1>
          <p className="text-gray-600">Thank you for your purchase. Your order has been confirmed.</p>
        </div>

        {/* Transaction Details */}
        <div className="bg-gray-50 rounded-lg p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4">Order Details</h2>
          
          {transactionId && (
            <div className="mb-3">
              <span className="text-gray-600">Transaction ID: </span>
              <span className="font-mono text-sm bg-gray-200 px-2 py-1 rounded">{transactionId}</span>
            </div>
          )}
          
          {amount && (
            <div className="mb-3">
              <span className="text-gray-600">Total Amount: </span>
              <span className="font-bold text-green-600">${amount.toFixed(2)}</span>
            </div>
          )}

          {items && items.length > 0 && (
            <div className="mt-4">
              <h3 className="font-semibold mb-2">Items Purchased:</h3>
              <div className="space-y-2">
                {items.map((item) => (
                  <div key={item.id} className="flex justify-between text-sm">
                    <span>{item.title} × {item.quantity}</span>
                    <span>${(parseFloat(item.price.replace('$', '')) * item.quantity).toFixed(2)}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Next Steps */}
        <div className="bg-blue-50 rounded-lg p-6 mb-6">
          <h3 className="font-semibold mb-3 flex items-center justify-center">
            <FaEnvelope className="mr-2" />
            What's Next?
          </h3>
          <ul className="text-sm text-gray-700 space-y-2">
            <li>• A confirmation email has been sent to your email address</li>
            <li>• Your order will be processed within 1-2 business days</li>
            <li>• You'll receive a tracking number once your order ships</li>
            <li>• Estimated delivery: 3-5 business days</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Link 
            to="/home" 
            className="w-full bg-[#575CEE] text-white py-3 px-4 rounded-md hover:bg-[#4a4fd1] transition-colors flex items-center justify-center"
          >
            <FaHome className="mr-2" />
            Continue Shopping
          </Link>
          
          <Link 
            to="/about" 
            className="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-md hover:bg-gray-50 transition-colors flex items-center justify-center"
          >
            <FaShoppingBag className="mr-2" />
            View All Products
          </Link>
        </div>

        {/* Support */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Need help? Contact our support team at{' '}
            <a href="mailto:<EMAIL>" className="text-[#575CEE] hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccess;
